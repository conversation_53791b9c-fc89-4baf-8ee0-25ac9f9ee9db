# OPTEE属性获取相关GP API设计文档

## 1. 概述

本文档详细分析OPTEE中属性获取相关的GlobalPlatform (GP) API设计，包括API函数实现、数据结构定义、属性类型、存储位置以及系统调用机制。

## 2. GP API函数列表

### 2.1 属性获取函数
- `TEE_GetPropertyAsString` - 获取字符串类型属性
- `TEE_GetPropertyAsBool` - 获取布尔类型属性  
- `TEE_GetPropertyAsU32` - 获取32位无符号整数属性
- `TEE_GetPropertyAsU64` - 获取64位无符号整数属性
- `TEE_GetPropertyAsBinaryBlock` - 获取二进制块属性
- `TEE_GetPropertyAsUUID` - 获取UUID类型属性
- `TEE_GetPropertyAsIdentity` - 获取身份标识属性

### 2.2 属性枚举函数
- `TEE_AllocatePropertyEnumerator` - 分配属性枚举器
- `TEE_FreePropertyEnumerator` - 释放属性枚举器
- `TEE_StartPropertyEnumerator` - 启动属性枚举
- `TEE_ResetPropertyEnumerator` - 重置属性枚举器
- `TEE_GetPropertyName` - 获取属性名称
- `TEE_GetNextProperty` - 获取下一个属性

## 3. 核心数据结构

### 3.1 属性类型枚举

```c
// lib/libutee/include/user_ta_header.h
enum user_ta_prop_type {
    USER_TA_PROP_TYPE_BOOL,         // bool类型
    USER_TA_PROP_TYPE_U32,          // uint32_t类型
    USER_TA_PROP_TYPE_UUID,         // TEE_UUID类型
    USER_TA_PROP_TYPE_IDENTITY,     // TEE_Identity类型
    USER_TA_PROP_TYPE_STRING,       // 零终止字符串
    USER_TA_PROP_TYPE_BINARY_BLOCK, // base64编码的二进制数据
    USER_TA_PROP_TYPE_U64,          // uint64_t类型
    USER_TA_PROP_TYPE_INVALID,      // 无效类型
};
```

### 3.2 用户TA属性结构

```c
// lib/libutee/include/user_ta_header.h
struct user_ta_property {
    const char *name;                    // 属性名称
    enum user_ta_prop_type type;         // 属性类型
    const void *value;                   // 属性值指针
};
```

### 3.3 TEE属性结构

```c
// core/include/tee/tee_svc.h
struct tee_props {
    const char *name;                    // 属性名称
    const uint32_t prop_type;           // 属性类型(enum user_ta_prop_type)
    
    // 获取属性的函数指针或静态数据
    TEE_Result (*get_prop_func)(struct ts_session *sess,
                               void *buf, size_t *blen);
    const void *data;                   // 静态属性数据
    const size_t len;                   // 数据长度
};
```

### 3.4 属性枚举器结构

```c
// lib/libutee/tee_api_property.c
struct prop_enumerator {
    uint32_t idx;                       // 当前索引
    TEE_PropSetHandle prop_set;         // 属性集句柄(TEE_PROPSET_xxx)
};
```

## 4. 基础数据类型

### 4.1 TEE_UUID结构

```c
// lib/libutee/include/tee_api_types.h
typedef struct {
    uint32_t timeLow;                   // 时间低32位
    uint16_t timeMid;                   // 时间中16位
    uint16_t timeHiAndVersion;          // 时间高16位和版本
    uint8_t clockSeqAndNode[8];         // 时钟序列和节点标识
} TEE_UUID;
```

### 4.2 TEE_Identity结构

```c
// lib/libutee/include/tee_api_types.h
typedef struct {
    uint32_t login;                     // 登录类型(TEE_LOGIN_xxx)
    TEE_UUID uuid;                      // 客户端UUID
} TEE_Identity;
```

### 4.3 属性集句柄类型

```c
// lib/libutee/include/tee_api_types.h
typedef struct __TEE_PropSetHandle *TEE_PropSetHandle;
```

### 4.4 属性集伪句柄定义

```c
// lib/libutee/include/tee_api_defines.h
#define TEE_PROPSET_TEE_IMPLEMENTATION  (TEE_PropSetHandle)0xFFFFFFFD
#define TEE_PROPSET_CURRENT_CLIENT      (TEE_PropSetHandle)0xFFFFFFFE  
#define TEE_PROPSET_CURRENT_TA          (TEE_PropSetHandle)0xFFFFFFFF
```

## 5. 属性存储位置和分类

### 5.1 TEE实现属性 (TEE_PROPSET_TEE_IMPLEMENTATION)

**位置**: `core/tee/tee_svc.c` - `tee_propset_tee[]`

**主要属性**:
- `gpd.tee.apiversion` - TEE API版本 (STRING)
- `gpd.tee.description` - TEE描述信息 (STRING)  
- `gpd.tee.deviceID` - 设备ID (UUID)
- `gpd.tee.systemTime.protectionLevel` - 系统时间保护级别 (U32)
- `gpd.tee.TAPersistentTime.protectionLevel` - TA持久时间保护级别 (U32)

### 5.2 当前客户端属性 (TEE_PROPSET_CURRENT_CLIENT)

**位置**: `core/tee/tee_svc.c` - `tee_propset_client[]`

**主要属性**:
- `gpd.client.identity` - 客户端身份标识 (IDENTITY)
- `gpd.client.endian` - 客户端字节序 (U32)

### 5.3 当前TA属性 (TEE_PROPSET_CURRENT_TA)

**位置**: `lib/libutee/tee_api_property.c` - `tee_props[]`

**主要属性**:
- `gpd.tee.arith.maxBigIntSize` - 大整数最大位数 (U32)
- `gpd.tee.sockets.version` - Socket版本 (U32)
- `gpd.tee.sockets.tcp.version` - TCP Socket版本 (U32)
- `gpd.tee.internalCore.version` - 内核API版本 (U32)

**扩展属性**: TA可通过`TA_CURRENT_TA_EXT_PROPERTIES`宏定义自定义属性
